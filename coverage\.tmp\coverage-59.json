{"result": [{"scriptId": "1037", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/test/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 377, "endOffset": 541, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 591, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 917, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1138, "endOffset": 1296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2178, "endOffset": 2538, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2654, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2896, "endOffset": 3038, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3283, "endOffset": 3386, "count": 25}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1305", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/hooks/__tests__/useLocalStorage.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 55781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 55781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 723, "endOffset": 5848, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 770, "endOffset": 853, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 955, "endOffset": 1185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1023, "endOffset": 1099, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1285, "endOffset": 1589, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1428, "endOffset": 1504, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1673, "endOffset": 2116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1741, "endOffset": 1811, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1849, "endOffset": 1904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2185, "endOffset": 2695, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2352, "endOffset": 2415, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2453, "endOffset": 2509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2485, "endOffset": 2501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2763, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2885, "endOffset": 2959, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3055, "endOffset": 3108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3395, "endOffset": 3836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3463, "endOffset": 3533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3571, "endOffset": 3626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3919, "endOffset": 4511, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4022, "endOffset": 4084, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4148, "endOffset": 4218, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4305, "endOffset": 4356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4592, "endOffset": 4872, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4720, "endOffset": 4791, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4936, "endOffset": 5389, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5004, "endOffset": 5069, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5107, "endOffset": 5157, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5270, "endOffset": 5314, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5454, "endOffset": 5844, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5558, "endOffset": 5631, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5706, "endOffset": 5758, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5908, "endOffset": 9887, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5955, "endOffset": 6040, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6144, "endOffset": 6376, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6212, "endOffset": 6290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6478, "endOffset": 6786, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6623, "endOffset": 6701, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6872, "endOffset": 7319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6940, "endOffset": 7012, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7050, "endOffset": 7105, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7397, "endOffset": 7808, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7465, "endOffset": 7537, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7575, "endOffset": 7630, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7893, "endOffset": 8316, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7947, "endOffset": 8009, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8073, "endOffset": 8145, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8183, "endOffset": 8234, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8399, "endOffset": 8683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8529, "endOffset": 8602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8770, "endOffset": 9236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8889, "endOffset": 8954, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8992, "endOffset": 9048, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9024, "endOffset": 9040, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9322, "endOffset": 9883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9514, "endOffset": 9579, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9617, "endOffset": 9670, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9969, "endOffset": 13735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10016, "endOffset": 10099, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10176, "endOffset": 10582, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10244, "endOffset": 10310, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10348, "endOffset": 10392, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10658, "endOffset": 11060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10726, "endOffset": 10788, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10826, "endOffset": 10870, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11136, "endOffset": 11515, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11204, "endOffset": 11274, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11386, "endOffset": 11435, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11591, "endOffset": 12004, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11747, "endOffset": 11817, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12071, "endOffset": 12670, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12139, "endOffset": 12209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12473, "endOffset": 12528, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12740, "endOffset": 13162, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12814, "endOffset": 12855, "count": 1000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12925, "endOffset": 12988, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13026, "endOffset": 13077, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13241, "endOffset": 13731, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13374, "endOffset": 13444, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13482, "endOffset": 13531, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1451", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/hooks/useLocalStorage.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20532, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 312, "endOffset": 360, "count": 29}, {"startOffset": 350, "endOffset": 358, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 13}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "useLocalStorage", "ranges": [{"startOffset": 1052, "endOffset": 3128, "count": 29}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1225, "endOffset": 1529, "count": 17}, {"startOffset": 1317, "endOffset": 1378, "count": 12}, {"startOffset": 1379, "endOffset": 1393, "count": 5}, {"startOffset": 1402, "endOffset": 1524, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1634, "endOffset": 2213, "count": 11}, {"startOffset": 1787, "endOffset": 1807, "count": 1}, {"startOffset": 1808, "endOffset": 1815, "count": 10}, {"startOffset": 1932, "endOffset": 1988, "count": 0}, {"startOffset": 2114, "endOffset": 2208, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2363, "endOffset": 2570, "count": 1}, {"startOffset": 2470, "endOffset": 2565, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2696, "endOffset": 3052, "count": 18}], "isBlockCoverage": true}, {"functionName": "handleStorageChange", "ranges": [{"startOffset": 2737, "endOffset": 2904, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2982, "endOffset": 3046, "count": 18}], "isBlockCoverage": true}, {"functionName": "useSessionStorage", "ranges": [{"startOffset": 3339, "endOffset": 4663, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3440, "endOffset": 3748, "count": 8}, {"startOffset": 3534, "endOffset": 3595, "count": 5}, {"startOffset": 3596, "endOffset": 3610, "count": 3}, {"startOffset": 3619, "endOffset": 3743, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3811, "endOffset": 4290, "count": 4}, {"startOffset": 3889, "endOffset": 3909, "count": 1}, {"startOffset": 3910, "endOffset": 3917, "count": 3}, {"startOffset": 4003, "endOffset": 4061, "count": 0}, {"startOffset": 4189, "endOffset": 4285, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4376, "endOffset": 4587, "count": 1}, {"startOffset": 4485, "endOffset": 4582, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1454", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/utils/helpers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63111, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 310, "endOffset": 356, "count": 17}, {"startOffset": 346, "endOffset": 354, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 465, "endOffset": 515, "count": 15}, {"startOffset": 505, "endOffset": 513, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 617, "endOffset": 660, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 760, "endOffset": 801, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 901, "endOffset": 942, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1044, "endOffset": 1087, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1184, "endOffset": 1222, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1321, "endOffset": 1361, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1462, "endOffset": 1504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1609, "endOffset": 1655, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1762, "endOffset": 1810, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1910, "endOffset": 1951, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2056, "endOffset": 2102, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2207, "endOffset": 2253, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2360, "endOffset": 2408, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2521, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2679, "endOffset": 2724, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2829, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2980, "endOffset": 3026, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3131, "endOffset": 3177, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3286, "endOffset": 3336, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3442, "endOffset": 3489, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3588, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3726, "endOffset": 3765, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3862, "endOffset": 3900, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3999, "endOffset": 4039, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4143, "endOffset": 4188, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4290, "endOffset": 4333, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4430, "endOffset": 4468, "count": 0}], "isBlockCoverage": false}, {"functionName": "safeJsonParse", "ranges": [{"startOffset": 4619, "endOffset": 4808, "count": 17}, {"startOffset": 4708, "endOffset": 4805, "count": 7}], "isBlockCoverage": true}, {"functionName": "safeJsonStringify", "ranges": [{"startOffset": 4839, "endOffset": 5024, "count": 15}, {"startOffset": 4918, "endOffset": 5021, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateId", "ranges": [{"startOffset": 5048, "endOffset": 5247, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 5444, "endOffset": 5610, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 5807, "endOffset": 6013, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatTime", "ranges": [{"startOffset": 6169, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "clamp", "ranges": [{"startOffset": 6636, "endOffset": 6708, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEmpty", "ranges": [{"startOffset": 6901, "endOffset": 7160, "count": 0}], "isBlockCoverage": false}, {"functionName": "deepClone", "ranges": [{"startOffset": 7283, "endOffset": 7709, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripHtmlTags", "ranges": [{"startOffset": 7850, "endOffset": 8026, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 8198, "endOffset": 8592, "count": 0}], "isBlockCoverage": false}, {"functionName": "isMobile", "ranges": [{"startOffset": 8699, "endOffset": 8745, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTouchDevice", "ranges": [{"startOffset": 8867, "endOffset": 8945, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateEmail", "ranges": [{"startOffset": 9114, "endOffset": 9273, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidAudioUrl", "ranges": [{"startOffset": 9434, "endOffset": 9644, "count": 0}], "isBlockCoverage": false}, {"functionName": "capitalizeFirstLetter", "ranges": [{"startOffset": 9815, "endOffset": 9933, "count": 0}], "isBlockCoverage": false}, {"functionName": "truncateText", "ranges": [{"startOffset": 10192, "endOffset": 10423, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEmptyObject", "ranges": [{"startOffset": 10568, "endOffset": 10697, "count": 0}], "isBlockCoverage": false}, {"functionName": "arrayToObject", "ranges": [{"startOffset": 10924, "endOffset": 11153, "count": 0}], "isBlockCoverage": false}, {"functionName": "objectToArray", "ranges": [{"startOffset": 11327, "endOffset": 11462, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEmptyValues", "ranges": [{"startOffset": 11618, "endOffset": 11884, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortByProperty", "ranges": [{"startOffset": 12132, "endOffset": 12463, "count": 0}], "isBlockCoverage": false}, {"functionName": "groupBy", "ranges": [{"startOffset": 12673, "endOffset": 12983, "count": 0}], "isBlockCoverage": false}, {"functionName": "unique", "ranges": [{"startOffset": 13135, "endOffset": 13222, "count": 0}], "isBlockCoverage": false}, {"functionName": "chunk", "ranges": [{"startOffset": 13396, "endOffset": 13611, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatten", "ranges": [{"startOffset": 13749, "endOffset": 13926, "count": 0}], "isBlockCoverage": false}, {"functionName": "intersection", "ranges": [{"startOffset": 14119, "endOffset": 14267, "count": 0}], "isBlockCoverage": false}, {"functionName": "difference", "ranges": [{"startOffset": 14459, "endOffset": 14608, "count": 0}], "isBlockCoverage": false}, {"functionName": "union", "ranges": [{"startOffset": 14780, "endOffset": 14934, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1455", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/utils/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16862, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16862, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 303, "endOffset": 342, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 444, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 589, "endOffset": 632, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 736, "endOffset": 781, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 896, "endOffset": 952, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1060, "endOffset": 1109, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1215, "endOffset": 1262, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1366, "endOffset": 1411, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1518, "endOffset": 1566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1668, "endOffset": 1711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1816, "endOffset": 1862, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1965, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2120, "endOffset": 2172, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}