import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import SignupPage from '../SignupPage.jsx';

// Mock components that might have complex dependencies
vi.mock('../../../components/common/AnimationBox', () => ({
  default: ({ children }) => <div data-testid="animation-box">{children}</div>
}));

vi.mock('../../../components/layout/Header', () => ({
  default: () => <div data-testid="header">Header</div>
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('SignupPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Rendering', () => {
    it('should render signup form elements', () => {
      renderWithRouter(<SignupPage />);

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('animation-box')).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument(); // Email input
      expect(screen.getByText('Continue')).toBeInTheDocument(); // Form submit button
    });

    it('should render login link', () => {
      renderWithRouter(<SignupPage />);

      expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
      expect(screen.getByText(/sign in/i)).toBeInTheDocument();
    });

    it('should render social login buttons', () => {
      renderWithRouter(<SignupPage />);

      expect(screen.getByText('Continue with Google')).toBeInTheDocument();
      expect(screen.getByText('Continue with Apple')).toBeInTheDocument();
      expect(screen.getByText('Continue with Microsoft')).toBeInTheDocument();
      expect(screen.getByText('Continue with Facebook')).toBeInTheDocument();
    });
  });

  describe('Form validation', () => {
    it('should validate email format', async () => {
      renderWithRouter(<SignupPage />);

      const emailInput = screen.getByRole('textbox');
      const submitButton = screen.getByRole('button', { name: 'Continue' });

      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });
    });

    it('should handle empty email', async () => {
      renderWithRouter(<SignupPage />);

      const submitButton = screen.getByRole('button', { name: 'Continue' });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });
    });
  });

  describe('Form submission', () => {
    it('should handle valid email submission', async () => {
      renderWithRouter(<SignupPage />);

      const emailInput = screen.getByRole('textbox');
      const submitButton = screen.getByRole('button', { name: 'Continue' });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/otp-verification');
      });
    });
  });

  describe('Navigation', () => {
    it('should navigate to login page when sign in link is clicked', () => {
      renderWithRouter(<SignupPage />);

      const signInLink = screen.getByText(/sign in/i);
      fireEvent.click(signInLink);

      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  describe('Input interactions', () => {
    it('should update email input value', () => {
      renderWithRouter(<SignupPage />);

      const emailInput = screen.getByRole('textbox');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(emailInput.value).toBe('<EMAIL>');
    });
  });

  describe('Error display', () => {
    it('should display validation errors', async () => {
      renderWithRouter(<SignupPage />);

      const emailInput = screen.getByRole('textbox');
      const submitButton = screen.getByRole('button', { name: 'Continue' });

      fireEvent.change(emailInput, { target: { value: 'invalid' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form structure', () => {
      renderWithRouter(<SignupPage />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      renderWithRouter(<SignupPage />);

      const emailInput = screen.getByRole('textbox');
      emailInput.focus();
      expect(document.activeElement).toBe(emailInput);
    });
  });
});
