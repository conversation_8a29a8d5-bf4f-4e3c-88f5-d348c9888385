import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import MainContent from '../MainContent.jsx';

// Mock the UI components
vi.mock('../../ui', () => ({
  LoadingSpinner: ({ size, message }) => (
    <div data-testid="loading-spinner" data-size={size}>
      {message}
    </div>
  ),
}));

// Mock the features components
vi.mock('../../features', () => ({
  PDFHighlighter: ({ pdfUrl, pageId, topicId, textSize }) => (
    <div
      data-testid="pdf-highlighter"
      data-pdf-url={pdfUrl}
      data-page-id={pageId}
      data-topic-id={topicId}
      data-text-size={textSize}
    >
      PDF Highlighter: {pdfUrl}
    </div>
  ),
}));

describe('MainContent Component', () => {
  describe('Loading state', () => {
    it('should render loading spinner when no topic is provided', () => {
      render(<MainContent topic={null} textSize="medium" />);

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.getByText('Loading content...')).toBeInTheDocument();
    });

    it('should render loading spinner when topic is undefined', () => {
      render(<MainContent topic={undefined} textSize="medium" />);

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.getByTestId('loading-spinner')).toHaveAttribute('data-size', 'large');
    });

    it('should render main element with correct class when loading', () => {
      const { container } = render(<MainContent topic={null} textSize="medium" />);

      const mainElement = container.querySelector('main.main-content-panel');
      expect(mainElement).toBeInTheDocument();
    });
  });

  describe('Content rendering', () => {
    const mockTopic = {
      id: 'topic-1',
      title: 'Test Topic',
      pdfUrl: '/test.pdf',
      content: {
        heading: 'Test Topic Heading',
        body: [
          {
            id: 'content-1',
            type: 'pdf',
            pdfUrl: '/test.pdf',
          },
        ],
      },
    };

    it('should render topic content with PDF highlighter', () => {
      render(<MainContent topic={mockTopic} textSize="medium" />);

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('article')).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getByTestId('pdf-highlighter')).toBeInTheDocument();
    });

    it('should display topic heading from content', () => {
      render(<MainContent topic={mockTopic} textSize="medium" />);

      expect(screen.getByText('Test Topic Heading')).toBeInTheDocument();
    });

    it('should fallback to topic title when no content heading', () => {
      const topicWithoutHeading = {
        ...mockTopic,
        content: {
          body: mockTopic.content.body,
        },
      };

      render(<MainContent topic={topicWithoutHeading} textSize="medium" />);

      expect(screen.getByText('Test Topic')).toBeInTheDocument();
    });

    it('should apply text size class to main element', () => {
      const { container } = render(<MainContent topic={mockTopic} textSize="large" />);

      const mainElement = container.querySelector('main');
      expect(mainElement).toHaveClass('main-content-panel', 'text-size-large');
    });
  });

  describe('PDF URL resolution', () => {
    it('should use direct pdfUrl property', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        pdfUrl: '/direct.pdf',
        content: {
          heading: 'Test Heading',
          body: [
            {
              id: 'content-1',
              type: 'pdf',
              pdfUrl: '/content.pdf',
            },
          ],
        },
      };

      render(<MainContent topic={topic} textSize="medium" />);

      const pdfHighlighter = screen.getByTestId('pdf-highlighter');
      expect(pdfHighlighter).toHaveAttribute('data-pdf-url', '/direct.pdf');
    });

    it('should fallback to content body PDF URL', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        content: {
          heading: 'Test Heading',
          body: [
            {
              id: 'content-1',
              type: 'pdf',
              pdfUrl: '/content.pdf',
            },
          ],
        },
      };

      render(<MainContent topic={topic} textSize="medium" />);

      const pdfHighlighter = screen.getByTestId('pdf-highlighter');
      expect(pdfHighlighter).toHaveAttribute('data-pdf-url', '/content.pdf');
    });

    it('should handle multiple content items and find PDF', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        content: {
          heading: 'Test Heading',
          body: [
            {
              id: 'content-1',
              type: 'text',
              content: 'Some text',
            },
            {
              id: 'content-2',
              type: 'pdf',
              pdfUrl: '/found.pdf',
            },
            {
              id: 'content-3',
              type: 'image',
              imageUrl: '/image.jpg',
            },
          ],
        },
      };

      render(<MainContent topic={topic} textSize="medium" />);

      const pdfHighlighter = screen.getByTestId('pdf-highlighter');
      expect(pdfHighlighter).toHaveAttribute('data-pdf-url', '/found.pdf');
    });

    it('should show no content message when no PDF URL found', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        content: {
          heading: 'Test Heading',
          body: [
            {
              id: 'content-1',
              type: 'text',
              content: 'Some text',
            },
          ],
        },
      };

      render(<MainContent topic={topic} textSize="medium" />);

      expect(screen.getByText('No PDF available for this topic.')).toBeInTheDocument();
      expect(screen.queryByTestId('pdf-highlighter')).not.toBeInTheDocument();
    });

    it('should show no content message when content body is empty', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        content: {
          heading: 'Test Heading',
          body: [],
        },
      };

      render(<MainContent topic={topic} textSize="medium" />);

      expect(screen.getByText('No PDF available for this topic.')).toBeInTheDocument();
    });

    it('should show no content message when content body is undefined', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        content: {
          heading: 'Test Heading',
        },
      };

      render(<MainContent topic={topic} textSize="medium" />);

      expect(screen.getByText('No PDF available for this topic.')).toBeInTheDocument();
    });
  });

  describe('PDFHighlighter props', () => {
    const mockTopic = {
      id: 'topic-1',
      title: 'Test Topic',
      pdfUrl: '/test.pdf',
      realTopicId: 'real-topic-1',
      content: {
        heading: 'Test Topic Heading',
      },
    };

    it('should pass correct props to PDFHighlighter', () => {
      render(<MainContent topic={mockTopic} textSize="small" />);

      const pdfHighlighter = screen.getByTestId('pdf-highlighter');
      expect(pdfHighlighter).toHaveAttribute('data-pdf-url', '/test.pdf');
      expect(pdfHighlighter).toHaveAttribute('data-page-id', 'topic-1');
      expect(pdfHighlighter).toHaveAttribute('data-topic-id', 'real-topic-1');
      expect(pdfHighlighter).toHaveAttribute('data-text-size', 'small');
    });

    it('should use topic id as topicId when realTopicId is not available', () => {
      const topicWithoutRealId = {
        ...mockTopic,
        realTopicId: undefined,
      };

      render(<MainContent topic={topicWithoutRealId} textSize="medium" />);

      const pdfHighlighter = screen.getByTestId('pdf-highlighter');
      expect(pdfHighlighter).toHaveAttribute('data-topic-id', 'topic-1');
    });
  });

  describe('Component structure', () => {
    const mockTopic = {
      id: 'topic-1',
      title: 'Test Topic',
      pdfUrl: '/test.pdf',
      content: {
        heading: 'Test Topic Heading',
      },
    };

    it('should have correct semantic HTML structure', () => {
      const { container } = render(<MainContent topic={mockTopic} textSize="medium" />);

      const main = container.querySelector('main.main-content-panel');
      const article = container.querySelector('article.lesson-content');
      const contentBody = container.querySelector('.lesson-content-body');

      expect(main).toBeInTheDocument();
      expect(article).toBeInTheDocument();
      expect(contentBody).toBeInTheDocument();

      // Check hierarchy
      expect(main).toContainElement(article);
      expect(article).toContainElement(contentBody);
    });

    it('should have correct CSS classes', () => {
      const { container } = render(<MainContent topic={mockTopic} textSize="large" />);

      expect(container.querySelector('.main-content-panel')).toBeInTheDocument();
      expect(container.querySelector('.text-size-large')).toBeInTheDocument();
      expect(container.querySelector('.lesson-content')).toBeInTheDocument();
      expect(container.querySelector('.lesson-content-body')).toBeInTheDocument();
    });
  });

  describe('Component type', () => {
    it('should be a React component', () => {
      expect(typeof MainContent).toBe('function');
    });
  });

  describe('Edge cases', () => {
    it('should handle topic with empty content object', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        content: {},
      };

      render(<MainContent topic={topic} textSize="medium" />);

      expect(screen.getByText('Test Topic')).toBeInTheDocument();
      expect(screen.getByText('No PDF available for this topic.')).toBeInTheDocument();
    });

    it('should handle topic without content property', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
      };

      render(<MainContent topic={topic} textSize="medium" />);

      expect(screen.getByText('Test Topic')).toBeInTheDocument();
      expect(screen.getByText('No PDF available for this topic.')).toBeInTheDocument();
    });

    it('should handle different text sizes', () => {
      const topic = {
        id: 'topic-1',
        title: 'Test Topic',
        pdfUrl: '/test.pdf',
      };

      const textSizes = ['small', 'medium', 'large', 'extra-large'];

      textSizes.forEach(size => {
        const { container, unmount } = render(<MainContent topic={topic} textSize={size} />);

        expect(container.querySelector(`.text-size-${size}`)).toBeInTheDocument();

        unmount();
      });
    });
  });
});
