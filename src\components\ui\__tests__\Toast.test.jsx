import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Toast, { ToastContainer } from '../Toast.jsx';

describe('Toast', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should render with default props', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} />);
    
    expect(screen.getByText('Test message')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('should render with different types', () => {
    const onClose = vi.fn();
    
    const { rerender } = render(
      <Toast id="test-1" message="Success message" type="success" onClose={onClose} />
    );
    expect(screen.getByText('✓')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toHaveClass('toast--success');
    
    rerender(<Toast id="test-2" message="Error message" type="error" onClose={onClose} />);
    expect(screen.getByText('✕')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toHaveClass('toast--error');
    
    rerender(<Toast id="test-3" message="Warning message" type="warning" onClose={onClose} />);
    expect(screen.getByText('⚠')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toHaveClass('toast--warning');
    
    rerender(<Toast id="test-4" message="Info message" type="info" onClose={onClose} />);
    expect(screen.getByText('ℹ')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toHaveClass('toast--info');
  });

  it('should show close button by default', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} />);
    
    const closeButton = screen.getByRole('button', { name: /close notification/i });
    expect(closeButton).toBeInTheDocument();
  });

  it('should hide close button when showCloseButton is false', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} showCloseButton={false} />);
    
    const closeButton = screen.queryByRole('button', { name: /close notification/i });
    expect(closeButton).not.toBeInTheDocument();
  });

  it('should call onClose when close button is clicked', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} />);
    
    const closeButton = screen.getByRole('button', { name: /close notification/i });
    fireEvent.click(closeButton);
    
    // Should trigger exit animation, then call onClose after delay
    vi.advanceTimersByTime(300);
    expect(onClose).toHaveBeenCalledWith('test-1');
  });

  it('should auto-close after duration', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} duration={3000} />);
    
    expect(onClose).not.toHaveBeenCalled();
    
    vi.advanceTimersByTime(3000);
    // Should trigger exit animation
    vi.advanceTimersByTime(300);
    
    expect(onClose).toHaveBeenCalledWith('test-1');
  });

  it('should not auto-close when duration is 0', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} duration={0} />);
    
    vi.advanceTimersByTime(10000); // Wait a long time
    
    expect(onClose).not.toHaveBeenCalled();
  });

  it('should have proper accessibility attributes', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} />);
    
    const toast = screen.getByRole('alert');
    expect(toast).toHaveAttribute('aria-live', 'polite');
  });

  it('should apply visibility classes correctly', async () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} />);
    
    const toast = screen.getByRole('alert');
    
    // Should start without visible class
    expect(toast).not.toHaveClass('toast--visible');
    
    // Should become visible after short delay
    vi.advanceTimersByTime(10);
    await waitFor(() => {
      expect(toast).toHaveClass('toast--visible');
    });
  });

  it('should apply exiting class when closing', () => {
    const onClose = vi.fn();
    render(<Toast id="test-1" message="Test message" onClose={onClose} />);
    
    const closeButton = screen.getByRole('button', { name: /close notification/i });
    const toast = screen.getByRole('alert');
    
    fireEvent.click(closeButton);
    
    expect(toast).toHaveClass('toast--exiting');
  });
});

describe('ToastContainer', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should render empty container when no toasts', () => {
    render(<ToastContainer toasts={[]} onRemoveToast={vi.fn()} />);
    
    const container = document.querySelector('.toast-container');
    expect(container).toBeInTheDocument();
    expect(container.children).toHaveLength(0);
  });

  it('should render multiple toasts', () => {
    const toasts = [
      { id: '1', message: 'First toast', type: 'info' },
      { id: '2', message: 'Second toast', type: 'success' },
    ];
    const onRemoveToast = vi.fn();
    
    render(<ToastContainer toasts={toasts} onRemoveToast={onRemoveToast} />);
    
    expect(screen.getByText('First toast')).toBeInTheDocument();
    expect(screen.getByText('Second toast')).toBeInTheDocument();
  });

  it('should pass onRemoveToast to individual toasts', () => {
    const toasts = [
      { id: '1', message: 'Test toast', type: 'info' },
    ];
    const onRemoveToast = vi.fn();
    
    render(<ToastContainer toasts={toasts} onRemoveToast={onRemoveToast} />);
    
    const closeButton = screen.getByRole('button', { name: /close notification/i });
    fireEvent.click(closeButton);
    
    vi.advanceTimersByTime(300);
    expect(onRemoveToast).toHaveBeenCalledWith('1');
  });

  it('should handle toast removal correctly', () => {
    const initialToasts = [
      { id: '1', message: 'First toast', type: 'info' },
      { id: '2', message: 'Second toast', type: 'success' },
    ];
    const onRemoveToast = vi.fn();
    
    const { rerender } = render(
      <ToastContainer toasts={initialToasts} onRemoveToast={onRemoveToast} />
    );
    
    expect(screen.getByText('First toast')).toBeInTheDocument();
    expect(screen.getByText('Second toast')).toBeInTheDocument();
    
    // Simulate removing first toast
    const updatedToasts = [
      { id: '2', message: 'Second toast', type: 'success' },
    ];
    
    rerender(<ToastContainer toasts={updatedToasts} onRemoveToast={onRemoveToast} />);
    
    expect(screen.queryByText('First toast')).not.toBeInTheDocument();
    expect(screen.getByText('Second toast')).toBeInTheDocument();
  });
});
