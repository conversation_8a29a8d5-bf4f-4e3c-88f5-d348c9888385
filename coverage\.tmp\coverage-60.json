{"result": [{"scriptId": "1037", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/test/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13104, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 377, "endOffset": 541, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 591, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 917, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1138, "endOffset": 1296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2178, "endOffset": 2538, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2654, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2896, "endOffset": 3038, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3283, "endOffset": 3386, "count": 17}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1305", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/__tests__/App.test.jsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 435, "endOffset": 492, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 556, "endOffset": 616, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 661, "endOffset": 2206, "count": 1}], "isBlockCoverage": true}, {"functionName": "LoginPage", "ranges": [{"startOffset": 683, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "SignupPage", "ranges": [{"startOffset": 962, "endOffset": 1227, "count": 0}], "isBlockCoverage": false}, {"functionName": "OTPVerificationPage", "ranges": [{"startOffset": 1252, "endOffset": 1524, "count": 0}], "isBlockCoverage": false}, {"functionName": "CreatePasswordPage", "ranges": [{"startOffset": 1548, "endOffset": 1831, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadingPage", "ranges": [{"startOffset": 1848, "endOffset": 2203, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2252, "endOffset": 2824, "count": 1}], "isBlockCoverage": true}, {"functionName": "PrivateRoute", "ranges": [{"startOffset": 2277, "endOffset": 2541, "count": 0}], "isBlockCoverage": false}, {"functionName": "PublicRoute", "ranges": [{"startOffset": 2558, "endOffset": 2821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2877, "endOffset": 3195, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToastContainer", "ranges": [{"startOffset": 2904, "endOffset": 3192, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3252, "endOffset": 3588, "count": 1}], "isBlockCoverage": true}, {"functionName": "Error<PERSON>ou<PERSON><PERSON>", "ranges": [{"startOffset": 3278, "endOffset": 3585, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4355, "endOffset": 4377, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4432, "endOffset": 23619, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4669, "endOffset": 5216, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4793, "endOffset": 4806, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4881, "endOffset": 4894, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5257, "endOffset": 5316, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5388, "endOffset": 7271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5480, "endOffset": 6307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6399, "endOffset": 7265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7330, "endOffset": 12297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7379, "endOffset": 7513, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7593, "endOffset": 8414, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8496, "endOffset": 9319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9421, "endOffset": 10251, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10351, "endOffset": 11192, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11301, "endOffset": 12291, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12355, "endOffset": 14604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12446, "endOffset": 13505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13580, "endOffset": 14598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14663, "endOffset": 15641, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14755, "endOffset": 15635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15713, "endOffset": 20502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15810, "endOffset": 16817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16903, "endOffset": 18038, "count": 1}, {"startOffset": 17826, "endOffset": 18037, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17107, "endOffset": 17213, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18142, "endOffset": 19290, "count": 1}, {"startOffset": 19162, "endOffset": 19289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18443, "endOffset": 18549, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19383, "endOffset": 20496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19661, "endOffset": 19767, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20395, "endOffset": 20474, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20568, "endOffset": 22437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20639, "endOffset": 21544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21614, "endOffset": 22431, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22492, "endOffset": 23615, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22583, "endOffset": 23609, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1468", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/App.jsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21356, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 304, "endOffset": 364, "count": 17}, {"startOffset": 354, "endOffset": 362, "count": 0}], "isBlockCoverage": true}, {"functionName": "App", "ranges": [{"startOffset": 2209, "endOffset": 11103, "count": 35}, {"startOffset": 9441, "endOffset": 9459, "count": 6}, {"startOffset": 9460, "endOffset": 9470, "count": 28}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2459, "endOffset": 2510, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2561, "endOffset": 3402, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 3722, "endOffset": 3977, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1469", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/styles/App.css", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11050, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11050, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10870, "endOffset": 10925, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1473", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/stores/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2621, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 310, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 482, "endOffset": 549, "count": 34}, {"startOffset": 539, "endOffset": 547, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 657, "endOffset": 728, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 833, "endOffset": 901, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1010, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1474", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/stores/audioStore.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 69561, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 69561, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 310, "endOffset": 356, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 812, "endOffset": 12987, "count": 1}], "isBlockCoverage": true}, {"functionName": "setError", "ranges": [{"startOffset": 1261, "endOffset": 1603, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearError", "ranges": [{"startOffset": 1622, "endOffset": 1714, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 1733, "endOffset": 4777, "count": 0}], "isBlockCoverage": false}, {"functionName": "play", "ranges": [{"startOffset": 4790, "endOffset": 7084, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 7098, "endOffset": 7316, "count": 0}], "isBlockCoverage": false}, {"functionName": "seek", "ranges": [{"startOffset": 7329, "endOffset": 7763, "count": 0}], "isBlockCoverage": false}, {"functionName": "setVolume", "ranges": [{"startOffset": 7781, "endOffset": 8046, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPlaybackRate", "ranges": [{"startOffset": 8070, "endOffset": 8340, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadTrack", "ranges": [{"startOffset": 8358, "endOffset": 9630, "count": 0}], "isBlockCoverage": false}, {"functionName": "updatePlaylist", "ranges": [{"startOffset": 9653, "endOffset": 9844, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadPagePlaylist", "ranges": [{"startOffset": 9909, "endOffset": 11607, "count": 0}], "isBlockCoverage": false}, {"functionName": "nextTrack", "ranges": [{"startOffset": 11625, "endOffset": 11836, "count": 0}], "isBlockCoverage": false}, {"functionName": "prevTrack", "ranges": [{"startOffset": 11854, "endOffset": 12047, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTrackIndex", "ranges": [{"startOffset": 12069, "endOffset": 12265, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentTrack", "ranges": [{"startOffset": 12311, "endOffset": 12410, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasNextTrack", "ranges": [{"startOffset": 12431, "endOffset": 12534, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasPrevTrack", "ranges": [{"startOffset": 12555, "endOffset": 12630, "count": 0}], "isBlockCoverage": false}, {"functionName": "reset", "ranges": [{"startOffset": 12644, "endOffset": 12982, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1478", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/utils/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16862, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16862, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 303, "endOffset": 342, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 444, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 589, "endOffset": 632, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 736, "endOffset": 781, "count": 1}, {"startOffset": 771, "endOffset": 779, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 896, "endOffset": 952, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1060, "endOffset": 1109, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1215, "endOffset": 1262, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1366, "endOffset": 1411, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1518, "endOffset": 1566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1668, "endOffset": 1711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1816, "endOffset": 1862, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1965, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2120, "endOffset": 2172, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1479", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/utils/helpers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63111, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 310, "endOffset": 356, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 465, "endOffset": 515, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 617, "endOffset": 660, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 760, "endOffset": 801, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 901, "endOffset": 942, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1044, "endOffset": 1087, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1184, "endOffset": 1222, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1321, "endOffset": 1361, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1462, "endOffset": 1504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1609, "endOffset": 1655, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1762, "endOffset": 1810, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1910, "endOffset": 1951, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2056, "endOffset": 2102, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2207, "endOffset": 2253, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2360, "endOffset": 2408, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2521, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2679, "endOffset": 2724, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2829, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2980, "endOffset": 3026, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3131, "endOffset": 3177, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3286, "endOffset": 3336, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3442, "endOffset": 3489, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3588, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3726, "endOffset": 3765, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3862, "endOffset": 3900, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3999, "endOffset": 4039, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4143, "endOffset": 4188, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4290, "endOffset": 4333, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4430, "endOffset": 4468, "count": 0}], "isBlockCoverage": false}, {"functionName": "safeJsonParse", "ranges": [{"startOffset": 4619, "endOffset": 4808, "count": 0}], "isBlockCoverage": false}, {"functionName": "safeJsonStringify", "ranges": [{"startOffset": 4839, "endOffset": 5024, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateId", "ranges": [{"startOffset": 5048, "endOffset": 5247, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 5444, "endOffset": 5610, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 5807, "endOffset": 6013, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatTime", "ranges": [{"startOffset": 6169, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "clamp", "ranges": [{"startOffset": 6636, "endOffset": 6708, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEmpty", "ranges": [{"startOffset": 6901, "endOffset": 7160, "count": 0}], "isBlockCoverage": false}, {"functionName": "deepClone", "ranges": [{"startOffset": 7283, "endOffset": 7709, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripHtmlTags", "ranges": [{"startOffset": 7850, "endOffset": 8026, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 8198, "endOffset": 8592, "count": 0}], "isBlockCoverage": false}, {"functionName": "isMobile", "ranges": [{"startOffset": 8699, "endOffset": 8745, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTouchDevice", "ranges": [{"startOffset": 8867, "endOffset": 8945, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateEmail", "ranges": [{"startOffset": 9114, "endOffset": 9273, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidAudioUrl", "ranges": [{"startOffset": 9434, "endOffset": 9644, "count": 0}], "isBlockCoverage": false}, {"functionName": "capitalizeFirstLetter", "ranges": [{"startOffset": 9815, "endOffset": 9933, "count": 0}], "isBlockCoverage": false}, {"functionName": "truncateText", "ranges": [{"startOffset": 10192, "endOffset": 10423, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEmptyObject", "ranges": [{"startOffset": 10568, "endOffset": 10697, "count": 0}], "isBlockCoverage": false}, {"functionName": "arrayToObject", "ranges": [{"startOffset": 10924, "endOffset": 11153, "count": 0}], "isBlockCoverage": false}, {"functionName": "objectToArray", "ranges": [{"startOffset": 11327, "endOffset": 11462, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEmptyValues", "ranges": [{"startOffset": 11618, "endOffset": 11884, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortByProperty", "ranges": [{"startOffset": 12132, "endOffset": 12463, "count": 0}], "isBlockCoverage": false}, {"functionName": "groupBy", "ranges": [{"startOffset": 12673, "endOffset": 12983, "count": 0}], "isBlockCoverage": false}, {"functionName": "unique", "ranges": [{"startOffset": 13135, "endOffset": 13222, "count": 0}], "isBlockCoverage": false}, {"functionName": "chunk", "ranges": [{"startOffset": 13396, "endOffset": 13611, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatten", "ranges": [{"startOffset": 13749, "endOffset": 13926, "count": 0}], "isBlockCoverage": false}, {"functionName": "intersection", "ranges": [{"startOffset": 14119, "endOffset": 14267, "count": 0}], "isBlockCoverage": false}, {"functionName": "difference", "ranges": [{"startOffset": 14459, "endOffset": 14608, "count": 0}], "isBlockCoverage": false}, {"functionName": "union", "ranges": [{"startOffset": 14780, "endOffset": 14934, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1480", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/stores/bookmarkStore.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34348, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 313, "endOffset": 362, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadFromStorage", "ranges": [{"startOffset": 605, "endOffset": 1068, "count": 1}, {"startOffset": 694, "endOffset": 862, "count": 0}, {"startOffset": 917, "endOffset": 1065, "count": 0}], "isBlockCoverage": true}, {"functionName": "saveToStorage", "ranges": [{"startOffset": 1095, "endOffset": 1390, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1520, "endOffset": 6370, "count": 1}], "isBlockCoverage": true}, {"functionName": "addBookmark", "ranges": [{"startOffset": 2000, "endOffset": 2383, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeBookmark", "ranges": [{"startOffset": 2501, "endOffset": 2774, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAnnotation", "ranges": [{"startOffset": 2895, "endOffset": 3386, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnnotation", "ranges": [{"startOffset": 3564, "endOffset": 3964, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeAnnotation", "ranges": [{"startOffset": 4091, "endOffset": 4384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBookmarks", "ranges": [{"startOffset": 4579, "endOffset": 5079, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAnnotations", "ranges": [{"startOffset": 5278, "endOffset": 5794, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPageBookmarked", "ranges": [{"startOffset": 5967, "endOffset": 6124, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAll", "ranges": [{"startOffset": 6205, "endOffset": 6360, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1481", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/stores/colorStore.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2836, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 310, "endOffset": 356, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 775, "endOffset": 890, "count": 1}], "isBlockCoverage": true}, {"functionName": "setSelectedColor", "ranges": [{"startOffset": 845, "endOffset": 885, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1482", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/stores/highlightStore.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56226, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56226, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 314, "endOffset": 364, "count": 0}], "isBlockCoverage": false}, {"functionName": "useHighlightStore.name", "ranges": [{"startOffset": 1717, "endOffset": 9195, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHasHydrated", "ranges": [{"startOffset": 1785, "endOffset": 1824, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHighlightMode", "ranges": [{"startOffset": 1991, "endOffset": 2037, "count": 0}], "isBlockCoverage": false}, {"functionName": "addHighlight", "ranges": [{"startOffset": 2062, "endOffset": 4743, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighlights", "ranges": [{"startOffset": 4769, "endOffset": 5095, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateHighlight", "ranges": [{"startOffset": 5123, "endOffset": 7230, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeHighlight", "ranges": [{"startOffset": 7258, "endOffset": 7999, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearHighlights", "ranges": [{"startOffset": 8027, "endOffset": 8632, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllHighlights", "ranges": [{"startOffset": 8661, "endOffset": 8951, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserHighlights", "ranges": [{"startOffset": 8981, "endOffset": 9084, "count": 0}], "isBlockCoverage": false}, {"functionName": "importHighlights", "ranges": [{"startOffset": 9113, "endOffset": 9187, "count": 0}], "isBlockCoverage": false}, {"functionName": "onRehydrateStorage", "ranges": [{"startOffset": 9284, "endOffset": 9448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9290, "endOffset": 9448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9627, "endOffset": 9677, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1484", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/hooks/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2716, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 308, "endOffset": 374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 486, "endOffset": 561, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 673, "endOffset": 748, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 855, "endOffset": 925, "count": 34}, {"startOffset": 915, "endOffset": 923, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1037, "endOffset": 1112, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1219, "endOffset": 1289, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1470, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1485", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/hooks/useDebounce.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11999, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 308, "endOffset": 352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 464, "endOffset": 517, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 629, "endOffset": 682, "count": 0}], "isBlockCoverage": false}, {"functionName": "useDebounce", "ranges": [{"startOffset": 1037, "endOffset": 1398, "count": 0}], "isBlockCoverage": false}, {"functionName": "useDebouncedCallback", "ranges": [{"startOffset": 1684, "endOffset": 2299, "count": 0}], "isBlockCoverage": false}, {"functionName": "useThrottledCallback", "ranges": [{"startOffset": 2585, "endOffset": 2974, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1486", "url": "file:///C:/Pryoms/somayya-academy-web-application/src/hooks/useLocalStorage.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20532, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 312, "endOffset": 360, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "useLocalStorage", "ranges": [{"startOffset": 1052, "endOffset": 3128, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSessionStorage", "ranges": [{"startOffset": 3339, "endOffset": 4663, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}