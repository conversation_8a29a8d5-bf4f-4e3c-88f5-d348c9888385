import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useError<PERSON>and<PERSON>, useAudioErrorHandler } from '../useErrorHandler.js';

describe('useErrorHandler', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('useErrorHandler', () => {
    it('should initialize with empty errors array', () => {
      const { result } = renderHook(() => useErrorHandler());
      
      expect(result.current.errors).toEqual([]);
      expect(typeof result.current.addError).toBe('function');
      expect(typeof result.current.removeError).toBe('function');
      expect(typeof result.current.clearErrors).toBe('function');
      expect(typeof result.current.handleAsyncError).toBe('function');
    });

    it('should add error with string message', () => {
      const { result } = renderHook(() => useErrorHandler());
      
      act(() => {
        result.current.addError('Test error message');
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Test error message');
      expect(result.current.errors[0].context).toBe('');
      expect(result.current.errors[0].id).toBeDefined();
      expect(result.current.errors[0].timestamp).toBeDefined();
    });

    it('should add error with Error object', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error object');
      
      act(() => {
        result.current.addError(error, 'Test context');
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Test error object');
      expect(result.current.errors[0].context).toBe('Test context');
    });

    it('should add multiple errors', () => {
      const { result } = renderHook(() => useErrorHandler());
      
      act(() => {
        result.current.addError('First error');
        result.current.addError('Second error');
      });
      
      expect(result.current.errors).toHaveLength(2);
      expect(result.current.errors[0].message).toBe('First error');
      expect(result.current.errors[1].message).toBe('Second error');
    });

    it('should remove specific error by ID', () => {
      const { result } = renderHook(() => useErrorHandler());
      let errorId;
      
      act(() => {
        errorId = result.current.addError('Test error');
        result.current.addError('Another error');
      });
      
      expect(result.current.errors).toHaveLength(2);
      
      act(() => {
        result.current.removeError(errorId);
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Another error');
    });

    it('should clear all errors', () => {
      const { result } = renderHook(() => useErrorHandler());
      
      act(() => {
        result.current.addError('First error');
        result.current.addError('Second error');
      });
      
      expect(result.current.errors).toHaveLength(2);
      
      act(() => {
        result.current.clearErrors();
      });
      
      expect(result.current.errors).toHaveLength(0);
    });

    it('should auto-remove errors after 5 seconds', () => {
      const { result } = renderHook(() => useErrorHandler());
      
      act(() => {
        result.current.addError('Auto-remove error');
      });
      
      expect(result.current.errors).toHaveLength(1);
      
      act(() => {
        vi.advanceTimersByTime(5000);
      });
      
      expect(result.current.errors).toHaveLength(0);
    });

    it('should handle async errors successfully', async () => {
      const { result } = renderHook(() => useErrorHandler());
      const successfulAsyncFn = vi.fn().mockResolvedValue('success');
      
      let returnValue;
      await act(async () => {
        returnValue = await result.current.handleAsyncError(successfulAsyncFn, 'Test context');
      });
      
      expect(returnValue).toBe('success');
      expect(result.current.errors).toHaveLength(0);
      expect(successfulAsyncFn).toHaveBeenCalledOnce();
    });

    it('should handle async errors that throw', async () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Async error');
      const failingAsyncFn = vi.fn().mockRejectedValue(error);
      
      await act(async () => {
        try {
          await result.current.handleAsyncError(failingAsyncFn, 'Test context');
        } catch (e) {
          expect(e).toBe(error);
        }
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Async error');
      expect(result.current.errors[0].context).toBe('Test context');
    });
  });

  describe('useAudioErrorHandler', () => {
    it('should inherit base error handler functionality', () => {
      const { result } = renderHook(() => useAudioErrorHandler());
      
      expect(result.current.errors).toEqual([]);
      expect(typeof result.current.addError).toBe('function');
      expect(typeof result.current.removeError).toBe('function');
      expect(typeof result.current.clearErrors).toBe('function');
      expect(typeof result.current.handleAsudioError).toBe('function');
    });

    it('should handle NotAllowedError with specific message', () => {
      const { result } = renderHook(() => useAudioErrorHandler());
      const error = new Error('Audio blocked');
      error.name = 'NotAllowedError';
      
      act(() => {
        result.current.handleAudioError(error, '/test-audio.mp3');
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Audio playback was blocked. Please interact with the page first.');
      expect(result.current.errors[0].context).toBe('Audio Player (/test-audio.mp3)');
    });

    it('should handle NotSupportedError with specific message', () => {
      const { result } = renderHook(() => useAudioErrorHandler());
      const error = new Error('Format not supported');
      error.name = 'NotSupportedError';
      
      act(() => {
        result.current.handleAudioError(error);
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('This audio format is not supported by your browser.');
      expect(result.current.errors[0].context).toBe('Audio Player');
    });

    it('should handle NetworkError with specific message', () => {
      const { result } = renderHook(() => useAudioErrorHandler());
      const error = new Error('Network failed');
      error.name = 'NetworkError';
      
      act(() => {
        result.current.handleAudioError(error, '/network-audio.mp3');
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Failed to load audio. Please check your internet connection.');
    });

    it('should handle generic audio errors', () => {
      const { result } = renderHook(() => useAudioErrorHandler());
      const error = new Error('Generic audio error');
      
      act(() => {
        result.current.handleAudioError(error);
      });
      
      expect(result.current.errors).toHaveLength(1);
      expect(result.current.errors[0].message).toBe('Audio playback failed. Please try again.');
    });
  });
});
