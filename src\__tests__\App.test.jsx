import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import App from '../App.jsx';
import { useAuthStore } from '../stores/authStore.js';
import { useErrorHandler } from '../hooks/useErrorHandler.js';

// Mock the stores and hooks
vi.mock('../stores/authStore.js', () => ({
  useAuthStore: vi.fn(),
}));

vi.mock('../hooks/useErrorHandler.js', () => ({
  useErrorHandler: vi.fn(),
}));

// Mock all page components
vi.mock('../pages', () => ({
  LoginPage: () => <div data-testid="login-page">Login Page</div>,
  SignupPage: () => <div data-testid="signup-page">Signup Page</div>,
  OTPVerificationPage: () => <div data-testid="otp-page">OTP Verification Page</div>,
  CreatePasswordPage: () => <div data-testid="create-password-page">Create Password Page</div>,
  ReadingPage: ({ courseData, pdfData }) => (
    <div data-testid="reading-page">
      Reading Page - {courseData?.title} - {pdfData?.title}
    </div>
  ),
}));

// Mock route components
vi.mock('../routes', () => ({
  PrivateRoute: ({ children }) => <div data-testid="private-route">{children}</div>,
  PublicRoute: ({ children }) => <div data-testid="public-route">{children}</div>,
}));

// Mock UI components
vi.mock('../components/ui', () => ({
  ToastContainer: (props) => <div data-testid="toast-container" {...props}>Toast Container</div>,
}));

// Mock common components
vi.mock('../components/common', () => ({
  ErrorBoundary: ({ children, onError, message }) => (
    <div data-testid="error-boundary" data-message={message}>
      {children}
    </div>
  ),
}));

// Mock AudioContext
const mockAudioContext = {
  state: 'suspended',
  resume: vi.fn().mockResolvedValue(undefined),
};

const mockAudioContextConstructor = vi.fn(() => mockAudioContext);

describe('App Component', () => {
  const mockErrorHandler = {
    error: null,
    handleError: vi.fn(),
    clearError: vi.fn(),
    addError: vi.fn(),
  };

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Mock console.log to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});

    // Setup default mock implementations
    useErrorHandler.mockReturnValue(mockErrorHandler);

    // Mock AudioContext
    global.AudioContext = mockAudioContextConstructor;
    global.webkitAudioContext = mockAudioContextConstructor;

    // Mock document event listeners
    document.addEventListener = vi.fn();
    document.removeEventListener = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Authentication-based routing', () => {
    it('should redirect to login when not authenticated', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      render(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      );

      // Should redirect to login (Navigate component behavior)
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });

    it('should redirect to reading screen when authenticated', () => {
      useAuthStore.mockReturnValue({
        currentUser: { id: 'user1', email: '<EMAIL>' },
        isAuthenticated: true,
      });

      render(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });
  });

  describe('Route rendering', () => {
    beforeEach(() => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });
    });

    it('should render login page on /login route', () => {
      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('public-route')).toBeInTheDocument();
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });

    it('should render signup page on /signup route', () => {
      render(
        <MemoryRouter initialEntries={['/signup']}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('public-route')).toBeInTheDocument();
      expect(screen.getByTestId('signup-page')).toBeInTheDocument();
    });

    it('should render OTP verification page on /otp-verification route', () => {
      render(
        <MemoryRouter initialEntries={['/otp-verification']}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('public-route')).toBeInTheDocument();
      expect(screen.getByTestId('otp-page')).toBeInTheDocument();
    });

    it('should render create password page on /create-password route', () => {
      render(
        <MemoryRouter initialEntries={['/create-password']}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('public-route')).toBeInTheDocument();
      expect(screen.getByTestId('create-password-page')).toBeInTheDocument();
    });

    it('should render reading page on /readingScreen route when authenticated', () => {
      useAuthStore.mockReturnValue({
        currentUser: { id: 'user1', email: '<EMAIL>' },
        isAuthenticated: true,
      });

      render(
        <MemoryRouter initialEntries={['/readingScreen']}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('private-route')).toBeInTheDocument();
      expect(screen.getByTestId('reading-page')).toBeInTheDocument();
    });
  });

  describe('Error handling', () => {
    it('should render ErrorBoundary with correct props', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      const errorBoundary = screen.getByTestId('error-boundary');
      expect(errorBoundary).toBeInTheDocument();
      expect(errorBoundary).toHaveAttribute(
        'data-message',
        'Something went wrong with the application. Please refresh the page.'
      );
    });

    it('should handle error boundary errors', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      const mockAddError = vi.fn();
      useErrorHandler.mockReturnValue({
        ...mockErrorHandler,
        addError: mockAddError,
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      // The ErrorBoundary is mocked, so we can't test the actual error handling
      // But we can verify the component structure is correct
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });
  });

  describe('Toast container', () => {
    it('should render ToastContainer with correct props', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      const toastContainer = screen.getByTestId('toast-container');
      expect(toastContainer).toBeInTheDocument();
    });
  });

  describe('Audio context initialization', () => {
    it('should set up audio context event listeners on mount', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      expect(document.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
      expect(document.addEventListener).toHaveBeenCalledWith('touchstart', expect.any(Function));
    });

    it('should initialize audio context on first click', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      // Mock the event listener to capture the handler
      let clickHandler;
      document.addEventListener.mockImplementation((event, handler) => {
        if (event === 'click') {
          clickHandler = handler;
        }
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      // Simulate first click
      if (clickHandler) {
        clickHandler();
      }

      expect(mockAudioContextConstructor).toHaveBeenCalled();
      expect(mockAudioContext.resume).toHaveBeenCalled();
    });

    it('should handle audio context when webkitAudioContext is available', () => {
      // Remove AudioContext and only provide webkitAudioContext
      delete global.AudioContext;
      global.webkitAudioContext = mockAudioContextConstructor;

      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      let clickHandler;
      document.addEventListener.mockImplementation((event, handler) => {
        if (event === 'click') {
          clickHandler = handler;
        }
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      if (clickHandler) {
        clickHandler();
      }

      expect(mockAudioContextConstructor).toHaveBeenCalled();
    });

    it('should handle case when AudioContext is not available', () => {
      delete global.AudioContext;
      delete global.webkitAudioContext;

      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      let clickHandler;
      document.addEventListener.mockImplementation((event, handler) => {
        if (event === 'click') {
          clickHandler = handler;
        }
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      // Should not throw error when AudioContext is not available
      expect(() => {
        if (clickHandler) {
          clickHandler();
        }
      }).not.toThrow();
    });
  });

  describe('Configuration handling', () => {
    it('should log config on mount', () => {
      const mockConfig = { apiUrl: 'https://api.example.com' };

      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App config={mockConfig} />
        </MemoryRouter>
      );

      expect(console.log).toHaveBeenCalledWith('App config:', mockConfig);
    });

    it('should handle undefined config', () => {
      useAuthStore.mockReturnValue({
        currentUser: null,
        isAuthenticated: false,
      });

      render(
        <MemoryRouter initialEntries={['/login']}>
          <App />
        </MemoryRouter>
      );

      expect(console.log).toHaveBeenCalledWith('App config:', undefined);
    });
  });

  describe('Course data', () => {
    it('should pass correct course data to ReadingPage', () => {
      useAuthStore.mockReturnValue({
        currentUser: { id: 'user1', email: '<EMAIL>' },
        isAuthenticated: true,
      });

      render(
        <MemoryRouter initialEntries={['/readingScreen']}>
          <App />
        </MemoryRouter>
      );

      const readingPage = screen.getByTestId('reading-page');
      expect(readingPage).toHaveTextContent('KEPH 102 - Physics Course');
      expect(readingPage).toHaveTextContent('KEPH 102');
    });
  });
});
