import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  safeJsonParse,
  safeJsonStringify,
  generateId,
  clamp,
  formatTime,
  debounce,
  throttle,
  getErrorMessage,
  validateEmail,
  isTouchDevice,
  isValidAudioUrl,
} from '../helpers.js';

describe('helpers.js', () => {
  describe('safeJsonParse', () => {
    it('should parse valid JSON string', () => {
      const jsonString = '{"name": "test", "value": 123}';
      const result = safeJsonParse(jsonString);
      expect(result).toEqual({ name: 'test', value: 123 });
    });

    it('should return default value for invalid JSON', () => {
      const invalidJson = '{"invalid": json}';
      const defaultValue = { error: true };
      const result = safeJsonParse(invalidJson, defaultValue);
      expect(result).toEqual(defaultValue);
    });

    it('should return null as default when no default provided', () => {
      const result = safeJsonParse('invalid json');
      expect(result).toBeNull();
    });
  });

  describe('safeJsonStringify', () => {
    it('should stringify valid object', () => {
      const obj = { name: 'test', value: 123 };
      const result = safeJsonStringify(obj);
      expect(result).toBe('{"name":"test","value":123}');
    });

    it('should return default value for unstringifiable object', () => {
      const circularObj = {};
      circularObj.self = circularObj;
      const result = safeJsonStringify(circularObj, '{"error":true}');
      expect(result).toBe('{"error":true}');
    });

    it('should return "{}" as default when no default provided', () => {
      const circularObj = {};
      circularObj.self = circularObj;
      const result = safeJsonStringify(circularObj);
      expect(result).toBe('{}');
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });

    it('should include prefix when provided', () => {
      const prefix = 'test';
      const id = generateId(prefix);
      expect(id).toMatch(new RegExp(`^${prefix}_`));
    });

    it('should not include prefix when empty string provided', () => {
      const id = generateId('');
      expect(id).not.toMatch(/^_/);
    });
  });

  describe('clamp', () => {
    it('should return value when within range', () => {
      expect(clamp(5, 0, 10)).toBe(5);
      expect(clamp(0, 0, 10)).toBe(0);
      expect(clamp(10, 0, 10)).toBe(10);
    });

    it('should clamp to minimum when value is below range', () => {
      expect(clamp(-5, 0, 10)).toBe(0);
    });

    it('should clamp to maximum when value is above range', () => {
      expect(clamp(15, 0, 10)).toBe(10);
    });

    it('should handle negative ranges', () => {
      expect(clamp(-5, -10, -1)).toBe(-5);
      expect(clamp(-15, -10, -1)).toBe(-10);
      expect(clamp(0, -10, -1)).toBe(-1);
    });
  });

  describe('formatTime', () => {
    it('should format seconds to MM:SS format', () => {
      expect(formatTime(0)).toBe('0:00');
      expect(formatTime(30)).toBe('0:30');
      expect(formatTime(60)).toBe('1:00');
      expect(formatTime(90)).toBe('1:30');
      expect(formatTime(3661)).toBe('61:01');
    });

    it('should handle invalid inputs', () => {
      expect(formatTime(NaN)).toBe('0:00');
      expect(formatTime(null)).toBe('0:00');
      expect(formatTime(undefined)).toBe('0:00');
      expect(formatTime(-10)).toBe('0:00');
    });
  });

  describe('debounce', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should delay function execution', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      expect(mockFn).not.toHaveBeenCalled();

      vi.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledOnce();
    });

    it('should cancel previous calls', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      vi.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledOnce();
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('test.example.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null)).toBe(false);
      expect(validateEmail(undefined)).toBe(false);
    });
  });

  describe('isValidAudioUrl', () => {
    it('should validate audio URLs', () => {
      expect(isValidAudioUrl('/audio.mp3')).toBe(true);
      expect(isValidAudioUrl('https://example.com/song.wav')).toBe(true);
      expect(isValidAudioUrl('/music.ogg')).toBe(true);
      expect(isValidAudioUrl('/sound.m4a')).toBe(true);
      expect(isValidAudioUrl('/audio.aac')).toBe(true);
    });

    it('should reject invalid audio URLs', () => {
      expect(isValidAudioUrl('/video.mp4')).toBe(false);
      expect(isValidAudioUrl('/document.pdf')).toBe(false);
      expect(isValidAudioUrl('')).toBe(false);
      expect(isValidAudioUrl(null)).toBe(false);
      expect(isValidAudioUrl(undefined)).toBe(false);
      expect(isValidAudioUrl(123)).toBe(false);
    });
  });

  describe('getErrorMessage', () => {
    it('should extract message from Error objects', () => {
      const error = new Error('Test error message');
      expect(getErrorMessage(error)).toBe('Test error message');
    });

    it('should handle string errors', () => {
      expect(getErrorMessage('String error')).toBe('String error');
    });

    it('should handle objects with message property', () => {
      const errorObj = { message: 'Object error message' };
      expect(getErrorMessage(errorObj)).toBe('Object error message');
    });

    it('should return generic message for unknown error types', () => {
      expect(getErrorMessage(null)).toBe('An unexpected error occurred');
      expect(getErrorMessage(undefined)).toBe('An unexpected error occurred');
      expect(getErrorMessage(123)).toBe('An unexpected error occurred');
      expect(getErrorMessage({})).toBe('An unexpected error occurred');
    });
  });
});
