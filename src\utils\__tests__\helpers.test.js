import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  safeJsonParse,
  safeJsonStringify,
  generateId,
  clamp,
  formatTime,
  debounce,
  throttle,
  getErrorMessage,
  validateEmail,
  isTouchDevice,
  isValidAudioUrl,
  stripHtmlTags,
  isMobile,
  capitalizeFirstLetter,
  truncateText,
  deepClone,
  isEmptyObject,
  arrayToObject,
  objectToArray,
  removeEmptyValues,
  sortByProperty,
  groupBy,
  unique,
  chunk,
  flatten,
  intersection,
  difference,
  union,
} from '../helpers.js';

describe('helpers.js', () => {
  describe('safeJsonParse', () => {
    it('should parse valid JSON string', () => {
      const jsonString = '{"name": "test", "value": 123}';
      const result = safeJsonParse(jsonString);
      expect(result).toEqual({ name: 'test', value: 123 });
    });

    it('should return default value for invalid JSON', () => {
      const invalidJson = '{"invalid": json}';
      const defaultValue = { error: true };
      const result = safeJsonParse(invalidJson, defaultValue);
      expect(result).toEqual(defaultValue);
    });

    it('should return null as default when no default provided', () => {
      const result = safeJsonParse('invalid json');
      expect(result).toBeNull();
    });
  });

  describe('safeJsonStringify', () => {
    it('should stringify valid object', () => {
      const obj = { name: 'test', value: 123 };
      const result = safeJsonStringify(obj);
      expect(result).toBe('{"name":"test","value":123}');
    });

    it('should return default value for unstringifiable object', () => {
      const circularObj = {};
      circularObj.self = circularObj;
      const result = safeJsonStringify(circularObj, '{"error":true}');
      expect(result).toBe('{"error":true}');
    });

    it('should return "{}" as default when no default provided', () => {
      const circularObj = {};
      circularObj.self = circularObj;
      const result = safeJsonStringify(circularObj);
      expect(result).toBe('{}');
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });

    it('should include prefix when provided', () => {
      const prefix = 'test';
      const id = generateId(prefix);
      expect(id).toMatch(new RegExp(`^${prefix}_`));
    });

    it('should not include prefix when empty string provided', () => {
      const id = generateId('');
      expect(id).not.toMatch(/^_/);
    });
  });

  describe('clamp', () => {
    it('should return value when within range', () => {
      expect(clamp(5, 0, 10)).toBe(5);
      expect(clamp(0, 0, 10)).toBe(0);
      expect(clamp(10, 0, 10)).toBe(10);
    });

    it('should clamp to minimum when value is below range', () => {
      expect(clamp(-5, 0, 10)).toBe(0);
    });

    it('should clamp to maximum when value is above range', () => {
      expect(clamp(15, 0, 10)).toBe(10);
    });

    it('should handle negative ranges', () => {
      expect(clamp(-5, -10, -1)).toBe(-5);
      expect(clamp(-15, -10, -1)).toBe(-10);
      expect(clamp(0, -10, -1)).toBe(-1);
    });
  });

  describe('formatTime', () => {
    it('should format seconds to MM:SS format', () => {
      expect(formatTime(0)).toBe('0:00');
      expect(formatTime(30)).toBe('0:30');
      expect(formatTime(60)).toBe('1:00');
      expect(formatTime(90)).toBe('1:30');
      expect(formatTime(3661)).toBe('61:01');
    });

    it('should handle invalid inputs', () => {
      expect(formatTime(NaN)).toBe('0:00');
      expect(formatTime(null)).toBe('0:00');
      expect(formatTime(undefined)).toBe('0:00');
      expect(formatTime(-10)).toBe('0:00');
    });
  });

  describe('debounce', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should delay function execution', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      expect(mockFn).not.toHaveBeenCalled();

      vi.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledOnce();
    });

    it('should cancel previous calls', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      vi.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledOnce();
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('test.example.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null)).toBe(false);
      expect(validateEmail(undefined)).toBe(false);
    });
  });

  describe('isValidAudioUrl', () => {
    it('should validate audio URLs', () => {
      expect(isValidAudioUrl('/audio.mp3')).toBe(true);
      expect(isValidAudioUrl('https://example.com/song.wav')).toBe(true);
      expect(isValidAudioUrl('/music.ogg')).toBe(true);
      expect(isValidAudioUrl('/sound.m4a')).toBe(true);
      expect(isValidAudioUrl('/audio.aac')).toBe(true);
    });

    it('should reject invalid audio URLs', () => {
      expect(isValidAudioUrl('/video.mp4')).toBe(false);
      expect(isValidAudioUrl('/document.pdf')).toBe(false);
      expect(isValidAudioUrl('')).toBe(false);
      expect(isValidAudioUrl(null)).toBe(false);
      expect(isValidAudioUrl(undefined)).toBe(false);
      expect(isValidAudioUrl(123)).toBe(false);
    });
  });

  describe('getErrorMessage', () => {
    it('should extract message from Error objects', () => {
      const error = new Error('Test error message');
      expect(getErrorMessage(error)).toBe('Test error message');
    });

    it('should handle string errors', () => {
      expect(getErrorMessage('String error')).toBe('String error');
    });

    it('should handle objects with message property', () => {
      const errorObj = { message: 'Object error message' };
      expect(getErrorMessage(errorObj)).toBe('Object error message');
    });

    it('should handle NotAllowedError', () => {
      const error = { name: 'NotAllowedError', message: 'Not allowed' };
      expect(getErrorMessage(error)).toBe('Failed to play audio');
    });

    it('should handle NotSupportedError', () => {
      const error = { name: 'NotSupportedError', message: 'Not supported' };
      expect(getErrorMessage(error)).toBe('Audio format not supported');
    });

    it('should return generic message for unknown error types', () => {
      expect(getErrorMessage(null)).toBe('An unexpected error occurred');
      expect(getErrorMessage(undefined)).toBe('An unexpected error occurred');
      expect(getErrorMessage(123)).toBe('An unexpected error occurred');
      expect(getErrorMessage({})).toBe('An unexpected error occurred');
    });
  });

  describe('stripHtmlTags', () => {
    it('should remove HTML tags from string', () => {
      const html = '<p>Hello <strong>world</strong>!</p>';
      expect(stripHtmlTags(html)).toBe('Hello world!');
    });

    it('should handle complex HTML', () => {
      const html = '<div><h1>Title</h1><p>Paragraph with <a href="#">link</a></p></div>';
      expect(stripHtmlTags(html)).toBe('TitleParagraph with link');
    });

    it('should handle empty or null input', () => {
      expect(stripHtmlTags('')).toBe('');
      expect(stripHtmlTags(null)).toBe('');
      expect(stripHtmlTags(undefined)).toBe('');
    });

    it('should handle plain text', () => {
      expect(stripHtmlTags('Plain text')).toBe('Plain text');
    });
  });

  describe('isMobile', () => {
    it('should detect mobile device', () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });
      expect(isMobile()).toBe(true);
    });

    it('should detect desktop device', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });
      expect(isMobile()).toBe(false);
    });

    it('should handle edge case at 768px', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });
      expect(isMobile()).toBe(true);
    });
  });

  describe('capitalizeFirstLetter', () => {
    it('should capitalize first letter', () => {
      expect(capitalizeFirstLetter('hello')).toBe('Hello');
      expect(capitalizeFirstLetter('world')).toBe('World');
    });

    it('should handle empty string', () => {
      expect(capitalizeFirstLetter('')).toBe('');
    });

    it('should handle single character', () => {
      expect(capitalizeFirstLetter('a')).toBe('A');
    });

    it('should handle already capitalized string', () => {
      expect(capitalizeFirstLetter('Hello')).toBe('Hello');
    });

    it('should handle non-string input', () => {
      expect(capitalizeFirstLetter(null)).toBe('');
      expect(capitalizeFirstLetter(undefined)).toBe('');
    });
  });

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const text = 'This is a very long text that should be truncated';
      expect(truncateText(text, 20)).toBe('This is a very long...');
    });

    it('should not truncate short text', () => {
      const text = 'Short text';
      expect(truncateText(text, 20)).toBe('Short text');
    });

    it('should handle custom suffix', () => {
      const text = 'This is a long text';
      expect(truncateText(text, 10, ' [more]')).toBe('This is a [more]');
    });

    it('should handle edge cases', () => {
      expect(truncateText('', 10)).toBe('');
      expect(truncateText(null, 10)).toBe('');
      expect(truncateText('text', 0)).toBe('...');
    });
  });

  describe('deepClone', () => {
    it('should deep clone objects', () => {
      const obj = { a: 1, b: { c: 2, d: [3, 4] } };
      const cloned = deepClone(obj);

      expect(cloned).toEqual(obj);
      expect(cloned).not.toBe(obj);
      expect(cloned.b).not.toBe(obj.b);
      expect(cloned.b.d).not.toBe(obj.b.d);
    });

    it('should handle arrays', () => {
      const arr = [1, { a: 2 }, [3, 4]];
      const cloned = deepClone(arr);

      expect(cloned).toEqual(arr);
      expect(cloned).not.toBe(arr);
      expect(cloned[1]).not.toBe(arr[1]);
    });

    it('should handle primitive values', () => {
      expect(deepClone(42)).toBe(42);
      expect(deepClone('string')).toBe('string');
      expect(deepClone(true)).toBe(true);
      expect(deepClone(null)).toBe(null);
    });
  });

  describe('isEmptyObject', () => {
    it('should detect empty objects', () => {
      expect(isEmptyObject({})).toBe(true);
      expect(isEmptyObject(Object.create(null))).toBe(true);
    });

    it('should detect non-empty objects', () => {
      expect(isEmptyObject({ a: 1 })).toBe(false);
      expect(isEmptyObject({ a: undefined })).toBe(false);
    });

    it('should handle non-objects', () => {
      expect(isEmptyObject(null)).toBe(true);
      expect(isEmptyObject(undefined)).toBe(true);
      expect(isEmptyObject('string')).toBe(true);
      expect(isEmptyObject([])).toBe(true);
    });
  });

  describe('arrayToObject', () => {
    it('should convert array to object with key function', () => {
      const arr = [{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }];
      const result = arrayToObject(arr, 'id');

      expect(result).toEqual({
        1: { id: 1, name: 'John' },
        2: { id: 2, name: 'Jane' }
      });
    });

    it('should handle function as key', () => {
      const arr = [{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }];
      const result = arrayToObject(arr, item => item.name.toLowerCase());

      expect(result).toEqual({
        john: { id: 1, name: 'John' },
        jane: { id: 2, name: 'Jane' }
      });
    });

    it('should handle empty array', () => {
      expect(arrayToObject([], 'id')).toEqual({});
    });
  });

  describe('objectToArray', () => {
    it('should convert object to array', () => {
      const obj = { a: 1, b: 2, c: 3 };
      const result = objectToArray(obj);

      expect(result).toEqual([
        { key: 'a', value: 1 },
        { key: 'b', value: 2 },
        { key: 'c', value: 3 }
      ]);
    });

    it('should handle empty object', () => {
      expect(objectToArray({})).toEqual([]);
    });
  });

  describe('removeEmptyValues', () => {
    it('should remove empty values from object', () => {
      const obj = { a: 1, b: '', c: null, d: undefined, e: 0, f: false };
      const result = removeEmptyValues(obj);

      expect(result).toEqual({ a: 1, e: 0, f: false });
    });

    it('should handle empty object', () => {
      expect(removeEmptyValues({})).toEqual({});
    });
  });

  describe('sortByProperty', () => {
    it('should sort array by property', () => {
      const arr = [{ name: 'John', age: 30 }, { name: 'Jane', age: 25 }, { name: 'Bob', age: 35 }];
      const result = sortByProperty(arr, 'age');

      expect(result[0].age).toBe(25);
      expect(result[1].age).toBe(30);
      expect(result[2].age).toBe(35);
    });

    it('should sort in descending order', () => {
      const arr = [{ name: 'John', age: 30 }, { name: 'Jane', age: 25 }];
      const result = sortByProperty(arr, 'age', 'desc');

      expect(result[0].age).toBe(30);
      expect(result[1].age).toBe(25);
    });

    it('should handle empty array', () => {
      expect(sortByProperty([], 'name')).toEqual([]);
    });
  });

  describe('groupBy', () => {
    it('should group array by property', () => {
      const arr = [
        { category: 'A', value: 1 },
        { category: 'B', value: 2 },
        { category: 'A', value: 3 }
      ];
      const result = groupBy(arr, 'category');

      expect(result.A).toHaveLength(2);
      expect(result.B).toHaveLength(1);
      expect(result.A[0].value).toBe(1);
      expect(result.A[1].value).toBe(3);
    });

    it('should handle function as key', () => {
      const arr = [{ age: 25 }, { age: 30 }, { age: 35 }];
      const result = groupBy(arr, item => item.age >= 30 ? 'senior' : 'junior');

      expect(result.junior).toHaveLength(1);
      expect(result.senior).toHaveLength(2);
    });
  });

  describe('unique', () => {
    it('should return unique values', () => {
      expect(unique([1, 2, 2, 3, 3, 3])).toEqual([1, 2, 3]);
      expect(unique(['a', 'b', 'a', 'c'])).toEqual(['a', 'b', 'c']);
    });

    it('should handle empty array', () => {
      expect(unique([])).toEqual([]);
    });
  });

  describe('chunk', () => {
    it('should split array into chunks', () => {
      const result = chunk([1, 2, 3, 4, 5], 2);
      expect(result).toEqual([[1, 2], [3, 4], [5]]);
    });

    it('should handle exact division', () => {
      const result = chunk([1, 2, 3, 4], 2);
      expect(result).toEqual([[1, 2], [3, 4]]);
    });

    it('should handle empty array', () => {
      expect(chunk([], 2)).toEqual([]);
    });
  });

  describe('flatten', () => {
    it('should flatten nested arrays', () => {
      const result = flatten([[1, 2], [3, 4], [5]]);
      expect(result).toEqual([1, 2, 3, 4, 5]);
    });

    it('should handle deeply nested arrays', () => {
      const result = flatten([[1, [2, 3]], [4, [5, 6]]]);
      expect(result).toEqual([1, 2, 3, 4, 5, 6]);
    });

    it('should handle empty array', () => {
      expect(flatten([])).toEqual([]);
    });
  });

  describe('intersection', () => {
    it('should find intersection of arrays', () => {
      const result = intersection([1, 2, 3], [2, 3, 4]);
      expect(result).toEqual([2, 3]);
    });

    it('should handle no intersection', () => {
      const result = intersection([1, 2], [3, 4]);
      expect(result).toEqual([]);
    });
  });

  describe('difference', () => {
    it('should find difference between arrays', () => {
      const result = difference([1, 2, 3], [2, 3, 4]);
      expect(result).toEqual([1]);
    });

    it('should handle no difference', () => {
      const result = difference([1, 2], [1, 2]);
      expect(result).toEqual([]);
    });
  });

  describe('union', () => {
    it('should find union of arrays', () => {
      const result = union([1, 2], [2, 3]);
      expect(result).toEqual([1, 2, 3]);
    });

    it('should handle duplicate values', () => {
      const result = union([1, 1, 2], [2, 3, 3]);
      expect(result).toEqual([1, 2, 3]);
    });
  });
});
