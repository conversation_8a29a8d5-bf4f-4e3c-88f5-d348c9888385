import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import OTPVerificationPage from '../OTPVerificationPage.jsx';

// Mock the stores
vi.mock('../../../stores/authStore', () => ({
  useAuthStore: vi.fn(),
}));

// Mock hooks
vi.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: vi.fn(),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('OTPVerificationPage', () => {
  const mockAuthStore = {
    verifyOTP: vi.fn(),
    resendOTP: vi.fn(),
    isLoading: false,
    error: null,
    user: { email: '<EMAIL>' },
  };

  const mockErrorHandler = {
    error: null,
    handleError: vi.fn(),
    clearError: vi.fn(),
    addError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    const { useAuthStore } = require('../../../stores/authStore');
    const { useErrorHandler } = require('../../../hooks/useErrorHandler');

    useAuthStore.mockReturnValue(mockAuthStore);
    useErrorHandler.mockReturnValue(mockErrorHandler);
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Rendering', () => {
    it('should render OTP verification form', () => {
      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByRole('heading', { name: /verify your email/i })).toBeInTheDocument();
      expect(screen.getByText(/enter the 6-digit code/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /verify/i })).toBeInTheDocument();
    });

    it('should render OTP input fields', () => {
      renderWithRouter(<OTPVerificationPage />);

      // Should have 6 OTP input fields
      const otpInputs = screen.getAllByRole('textbox');
      expect(otpInputs).toHaveLength(6);

      otpInputs.forEach(input => {
        expect(input).toHaveAttribute('maxLength', '1');
      });
    });

    it('should display user email', () => {
      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
    });

    it('should render resend code button', () => {
      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByRole('button', { name: /resend code/i })).toBeInTheDocument();
    });
  });

  describe('OTP input functionality', () => {
    it('should handle OTP input changes', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');

      fireEvent.change(otpInputs[0], { target: { value: '1' } });
      fireEvent.change(otpInputs[1], { target: { value: '2' } });
      fireEvent.change(otpInputs[2], { target: { value: '3' } });

      expect(otpInputs[0].value).toBe('1');
      expect(otpInputs[1].value).toBe('2');
      expect(otpInputs[2].value).toBe('3');
    });

    it('should auto-focus next input on digit entry', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');

      fireEvent.change(otpInputs[0], { target: { value: '1' } });
      expect(document.activeElement).toBe(otpInputs[1]);

      fireEvent.change(otpInputs[1], { target: { value: '2' } });
      expect(document.activeElement).toBe(otpInputs[2]);
    });

    it('should handle backspace navigation', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');

      // Focus on second input and press backspace
      otpInputs[1].focus();
      fireEvent.keyDown(otpInputs[1], { key: 'Backspace' });

      expect(document.activeElement).toBe(otpInputs[0]);
    });

    it('should only allow numeric input', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');

      fireEvent.change(otpInputs[0], { target: { value: 'a' } });
      expect(otpInputs[0].value).toBe('');

      fireEvent.change(otpInputs[0], { target: { value: '5' } });
      expect(otpInputs[0].value).toBe('5');
    });
  });

  describe('Form submission', () => {
    it('should handle successful OTP verification', async () => {
      mockAuthStore.verifyOTP.mockResolvedValue({ success: true });

      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');
      const verifyButton = screen.getByRole('button', { name: /verify/i });

      // Fill in complete OTP
      '123456'.split('').forEach((digit, index) => {
        fireEvent.change(otpInputs[index], { target: { value: digit } });
      });

      fireEvent.click(verifyButton);

      await waitFor(() => {
        expect(mockAuthStore.verifyOTP).toHaveBeenCalledWith('123456');
        expect(mockNavigate).toHaveBeenCalledWith('/create-password');
      });
    });

    it('should handle OTP verification failure', async () => {
      mockAuthStore.verifyOTP.mockRejectedValue(new Error('Invalid OTP'));

      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');
      const verifyButton = screen.getByRole('button', { name: /verify/i });

      // Fill in complete OTP
      '123456'.split('').forEach((digit, index) => {
        fireEvent.change(otpInputs[index], { target: { value: digit } });
      });

      fireEvent.click(verifyButton);

      await waitFor(() => {
        expect(mockErrorHandler.addError).toHaveBeenCalledWith(
          expect.objectContaining({
            message: expect.stringContaining('verification'),
            type: 'error'
          })
        );
      });
    });

    it('should require complete OTP before submission', async () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');
      const verifyButton = screen.getByRole('button', { name: /verify/i });

      // Fill in incomplete OTP
      fireEvent.change(otpInputs[0], { target: { value: '1' } });
      fireEvent.change(otpInputs[1], { target: { value: '2' } });

      fireEvent.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter complete otp/i)).toBeInTheDocument();
      });
    });

    it('should disable verify button during loading', () => {
      mockAuthStore.isLoading = true;

      renderWithRouter(<OTPVerificationPage />);

      const verifyButton = screen.getByRole('button', { name: /verify/i });
      expect(verifyButton).toBeDisabled();
    });
  });

  describe('Resend OTP functionality', () => {
    it('should handle resend OTP', async () => {
      mockAuthStore.resendOTP.mockResolvedValue({ success: true });

      renderWithRouter(<OTPVerificationPage />);

      const resendButton = screen.getByRole('button', { name: /resend code/i });
      fireEvent.click(resendButton);

      await waitFor(() => {
        expect(mockAuthStore.resendOTP).toHaveBeenCalled();
      });
    });

    it('should show countdown after resend', async () => {
      mockAuthStore.resendOTP.mockResolvedValue({ success: true });

      renderWithRouter(<OTPVerificationPage />);

      const resendButton = screen.getByRole('button', { name: /resend code/i });
      fireEvent.click(resendButton);

      await waitFor(() => {
        expect(resendButton).toBeDisabled();
      });

      // Fast-forward time
      vi.advanceTimersByTime(30000); // 30 seconds

      await waitFor(() => {
        expect(screen.getByText(/resend in 30s/i)).toBeInTheDocument();
      });
    });

    it('should enable resend button after countdown', async () => {
      mockAuthStore.resendOTP.mockResolvedValue({ success: true });

      renderWithRouter(<OTPVerificationPage />);

      const resendButton = screen.getByRole('button', { name: /resend code/i });
      fireEvent.click(resendButton);

      // Fast-forward past countdown
      vi.advanceTimersByTime(61000); // 61 seconds

      await waitFor(() => {
        expect(resendButton).not.toBeDisabled();
      });
    });
  });

  describe('Navigation', () => {
    it('should navigate back to signup', () => {
      renderWithRouter(<OTPVerificationPage />);

      const backButton = screen.getByRole('button', { name: /back to signup/i });
      fireEvent.click(backButton);

      expect(mockNavigate).toHaveBeenCalledWith('/signup');
    });
  });

  describe('Error handling', () => {
    it('should display auth store errors', () => {
      mockAuthStore.error = 'OTP expired';

      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByText('OTP expired')).toBeInTheDocument();
    });

    it('should clear errors on retry', async () => {
      renderWithRouter(<OTPVerificationPage />);

      const verifyButton = screen.getByRole('button', { name: /verify/i });

      fireEvent.click(verifyButton);
      fireEvent.click(verifyButton);

      await waitFor(() => {
        expect(mockErrorHandler.clearError).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');
      otpInputs.forEach((input, index) => {
        expect(input).toHaveAttribute('aria-label', `OTP digit ${index + 1}`);
      });
    });

    it('should support keyboard navigation', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');

      otpInputs[0].focus();
      expect(document.activeElement).toBe(otpInputs[0]);

      fireEvent.keyDown(otpInputs[0], { key: 'ArrowRight' });
      expect(document.activeElement).toBe(otpInputs[1]);

      fireEvent.keyDown(otpInputs[1], { key: 'ArrowLeft' });
      expect(document.activeElement).toBe(otpInputs[0]);
    });
  });

  describe('Auto-submission', () => {
    it('should auto-submit when all digits are entered', async () => {
      mockAuthStore.verifyOTP.mockResolvedValue({ success: true });

      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('textbox');

      // Fill in complete OTP
      '123456'.split('').forEach((digit, index) => {
        fireEvent.change(otpInputs[index], { target: { value: digit } });
      });

      await waitFor(() => {
        expect(mockAuthStore.verifyOTP).toHaveBeenCalledWith('123456');
      });
    });
  });
});
