import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ReadingPage from '../ReadingPage.jsx';

// Mock the stores individually
vi.mock('../../stores/authStore', () => ({
  useAuthStore: vi.fn(),
}));

vi.mock('../../stores/colorStore', () => ({
  useColorStore: vi.fn(),
}));

vi.mock('../../stores/bookmarkStore', () => ({
  useBookmarkStore: vi.fn(),
}));

vi.mock('../../stores/highlightStore', () => ({
  useHighlightStore: vi.fn(),
}));

vi.mock('../../stores/audioStore', () => ({
  useAudioStore: vi.fn(),
}));

// Mock components
vi.mock('../../components/layout', () => ({
  Header: ({ onToggleSidebar, onToggleTools }) => (
    <div data-testid="header">
      <button onClick={onToggleSidebar} data-testid="toggle-sidebar">Toggle Sidebar</button>
      <button onClick={onToggleTools} data-testid="toggle-tools">Toggle Tools</button>
    </div>
  ),
  CourseStructure: ({ isVisible, courseData, onTopicSelect }) => (
    <div data-testid="course-structure" style={{ display: isVisible ? 'block' : 'none' }}>
      <button onClick={() => onTopicSelect(courseData?.topics?.[0])} data-testid="topic-button">
        {courseData?.topics?.[0]?.title || 'Topic'}
      </button>
    </div>
  ),
  MainContent: ({ pdfData, isToolsPanelVisible }) => (
    <div data-testid="main-content" data-tools-visible={isToolsPanelVisible}>
      <div data-testid="pdf-content">{pdfData?.title || 'PDF Content'}</div>
    </div>
  ),
  ToolsPanel: ({ isVisible, onClose }) => (
    <div data-testid="tools-panel" style={{ display: isVisible ? 'block' : 'none' }}>
      <button onClick={onClose} data-testid="close-tools">Close</button>
    </div>
  ),
}));

// Mock UI components
vi.mock('../../components/ui', () => ({
  LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>,
}));

// Mock hooks
vi.mock('../../hooks/useErrorHandler', () => ({
  useErrorHandler: vi.fn(),
}));

describe('ReadingPage', () => {
  const mockCourseData = {
    id: '1',
    title: 'Test Course',
    topics: [
      {
        id: 'topic1',
        title: 'Topic 1',
        pdfUrl: '/test.pdf',
        content: { heading: 'Test Heading' }
      },
      {
        id: 'topic2',
        title: 'Topic 2',
        pdfUrl: '/test2.pdf',
        content: { heading: 'Test Heading 2' }
      }
    ]
  };

  const mockPdfData = mockCourseData.topics[0];

  const mockStores = {
    useAuthStore: {
      currentUser: { id: 'user1', email: '<EMAIL>' },
      isAuthenticated: true,
    },
    useColorStore: {
      currentTheme: 'light',
      setTheme: vi.fn(),
    },
    useBookmarkStore: {
      bookmarks: [],
      addBookmark: vi.fn(),
    },
    useHighlightStore: {
      highlights: [],
      addHighlight: vi.fn(),
    },
    useAudioStore: {
      isPlaying: false,
      currentTime: 0,
      play: vi.fn(),
      pause: vi.fn(),
    },
  };

  const mockErrorHandler = {
    error: null,
    handleError: vi.fn(),
    clearError: vi.fn(),
    addError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup store mocks
    const { useAuthStore } = require('../../stores/authStore');
    const { useColorStore } = require('../../stores/colorStore');
    const { useBookmarkStore } = require('../../stores/bookmarkStore');
    const { useHighlightStore } = require('../../stores/highlightStore');
    const { useAudioStore } = require('../../stores/audioStore');
    const { useErrorHandler } = require('../../hooks/useErrorHandler');

    useAuthStore.mockReturnValue(mockStores.useAuthStore);
    useColorStore.mockReturnValue(mockStores.useColorStore);
    useBookmarkStore.mockReturnValue(mockStores.useBookmarkStore);
    useHighlightStore.mockReturnValue(mockStores.useHighlightStore);
    useAudioStore.mockReturnValue(mockStores.useAudioStore);
    useErrorHandler.mockReturnValue(mockErrorHandler);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Rendering', () => {
    it('should render reading page with all components', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('course-structure')).toBeInTheDocument();
      expect(screen.getByTestId('main-content')).toBeInTheDocument();
      expect(screen.getByTestId('tools-panel')).toBeInTheDocument();
    });

    it('should render with loading state when no data provided', () => {
      render(
        <MemoryRouter>
          <ReadingPage />
        </MemoryRouter>
      );

      expect(screen.getByTestId('header')).toBeInTheDocument();
    });

    it('should pass correct props to MainContent', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      const mainContent = screen.getByTestId('main-content');
      expect(mainContent).toHaveAttribute('data-tools-visible', 'false');
      expect(screen.getByTestId('pdf-content')).toHaveTextContent('Topic 1');
    });
  });

  describe('Sidebar functionality', () => {
    it('should toggle sidebar visibility', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      const courseStructure = screen.getByTestId('course-structure');
      const toggleButton = screen.getByTestId('toggle-sidebar');

      // Initially visible
      expect(courseStructure).toHaveStyle({ display: 'block' });

      // Toggle to hide
      fireEvent.click(toggleButton);
      expect(courseStructure).toHaveStyle({ display: 'none' });

      // Toggle to show
      fireEvent.click(toggleButton);
      expect(courseStructure).toHaveStyle({ display: 'block' });
    });

    it('should handle topic selection', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      const topicButton = screen.getByTestId('topic-button');
      fireEvent.click(topicButton);

      // Should update the displayed content
      expect(screen.getByTestId('pdf-content')).toHaveTextContent('Topic 1');
    });
  });

  describe('Tools panel functionality', () => {
    it('should toggle tools panel visibility', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      const toolsPanel = screen.getByTestId('tools-panel');
      const toggleButton = screen.getByTestId('toggle-tools');
      const mainContent = screen.getByTestId('main-content');

      // Initially hidden
      expect(toolsPanel).toHaveStyle({ display: 'none' });
      expect(mainContent).toHaveAttribute('data-tools-visible', 'false');

      // Toggle to show
      fireEvent.click(toggleButton);
      expect(toolsPanel).toHaveStyle({ display: 'block' });
      expect(mainContent).toHaveAttribute('data-tools-visible', 'true');

      // Close tools panel
      const closeButton = screen.getByTestId('close-tools');
      fireEvent.click(closeButton);
      expect(toolsPanel).toHaveStyle({ display: 'none' });
      expect(mainContent).toHaveAttribute('data-tools-visible', 'false');
    });
  });

  describe('Error handling', () => {
    it('should handle missing course data gracefully', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={null} pdfData={null} />
        </MemoryRouter>
      );

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('main-content')).toBeInTheDocument();
    });

    it('should handle empty course data', () => {
      const emptyCourseData = { id: '1', title: 'Empty Course', topics: [] };

      render(
        <MemoryRouter>
          <ReadingPage courseData={emptyCourseData} pdfData={null} />
        </MemoryRouter>
      );

      expect(screen.getByTestId('course-structure')).toBeInTheDocument();
    });
  });

  describe('Responsive behavior', () => {
    it('should handle window resize events', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      // Simulate window resize
      global.innerWidth = 768;
      fireEvent(window, new Event('resize'));

      expect(screen.getByTestId('main-content')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      expect(screen.getByTestId('toggle-sidebar')).toBeInTheDocument();
      expect(screen.getByTestId('toggle-tools')).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      render(
        <MemoryRouter>
          <ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />
        </MemoryRouter>
      );

      const toggleButton = screen.getByTestId('toggle-sidebar');
      toggleButton.focus();
      expect(document.activeElement).toBe(toggleButton);
    });
  });
});
