import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import LoadingSpinner, { InlineLoader, SkeletonLoader } from '../LoadingSpinner.jsx';

describe('LoadingSpinner', () => {
  it('should render with default props', () => {
    render(<LoadingSpinner />);
    
    const spinner = screen.getByRole('status', { hidden: true });
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass('loading-spinner');
    expect(spinner).toHaveClass('loading-spinner--medium');
    expect(spinner).toHaveClass('loading-spinner--default');
  });

  it('should render with custom size', () => {
    render(<LoadingSpinner size="large" />);
    
    const spinner = screen.getByRole('status', { hidden: true });
    expect(spinner).toHaveClass('loading-spinner--large');
  });

  it('should render with custom variant', () => {
    render(<LoadingSpinner variant="primary" />);
    
    const spinner = screen.getByRole('status', { hidden: true });
    expect(spinner).toHaveClass('loading-spinner--primary');
  });

  it('should render with message', () => {
    const message = 'Loading content...';
    render(<LoadingSpinner message={message} />);
    
    expect(screen.getByText(message)).toBeInTheDocument();
  });

  it('should render with custom className', () => {
    const customClass = 'custom-spinner';
    render(<LoadingSpinner className={customClass} />);
    
    const spinner = screen.getByRole('status', { hidden: true });
    expect(spinner).toHaveClass(customClass);
  });

  it('should render with overlay', () => {
    render(<LoadingSpinner overlay />);
    
    const overlay = document.querySelector('.loading-spinner-overlay');
    expect(overlay).toBeInTheDocument();
  });

  it('should render without overlay by default', () => {
    render(<LoadingSpinner />);
    
    const overlay = document.querySelector('.loading-spinner-overlay');
    expect(overlay).not.toBeInTheDocument();
  });

  it('should render spinner circle', () => {
    render(<LoadingSpinner />);
    
    const circle = document.querySelector('.loading-spinner__circle');
    const inner = document.querySelector('.loading-spinner__inner');
    
    expect(circle).toBeInTheDocument();
    expect(inner).toBeInTheDocument();
  });

  it('should not render message when not provided', () => {
    render(<LoadingSpinner />);
    
    const messageElement = document.querySelector('.loading-spinner__message');
    expect(messageElement).not.toBeInTheDocument();
  });
});

describe('InlineLoader', () => {
  it('should render with default props', () => {
    render(<InlineLoader />);
    
    const loader = document.querySelector('.inline-loader');
    expect(loader).toBeInTheDocument();
    expect(loader).toHaveClass('inline-loader--small');
  });

  it('should render with custom size', () => {
    render(<InlineLoader size="medium" />);
    
    const loader = document.querySelector('.inline-loader');
    expect(loader).toHaveClass('inline-loader--medium');
  });

  it('should render with custom className', () => {
    const customClass = 'custom-inline-loader';
    render(<InlineLoader className={customClass} />);
    
    const loader = document.querySelector('.inline-loader');
    expect(loader).toHaveClass(customClass);
  });

  it('should render three dots', () => {
    render(<InlineLoader />);
    
    const dots = document.querySelectorAll('.inline-loader__dot');
    expect(dots).toHaveLength(3);
  });
});

describe('SkeletonLoader', () => {
  it('should render with default props', () => {
    render(<SkeletonLoader />);
    
    const skeleton = document.querySelector('.skeleton-loader');
    expect(skeleton).toBeInTheDocument();
    expect(skeleton).toHaveStyle({
      width: '100%',
      height: '20px',
      borderRadius: '4px',
    });
  });

  it('should render with custom dimensions', () => {
    render(<SkeletonLoader width="200px" height="40px" borderRadius="8px" />);
    
    const skeleton = document.querySelector('.skeleton-loader');
    expect(skeleton).toHaveStyle({
      width: '200px',
      height: '40px',
      borderRadius: '8px',
    });
  });

  it('should render with custom className', () => {
    const customClass = 'custom-skeleton';
    render(<SkeletonLoader className={customClass} />);
    
    const skeleton = document.querySelector('.skeleton-loader');
    expect(skeleton).toHaveClass(customClass);
  });

  it('should handle numeric width and height', () => {
    render(<SkeletonLoader width={150} height={30} />);
    
    const skeleton = document.querySelector('.skeleton-loader');
    expect(skeleton).toHaveStyle({
      width: '150',
      height: '30',
    });
  });
});
